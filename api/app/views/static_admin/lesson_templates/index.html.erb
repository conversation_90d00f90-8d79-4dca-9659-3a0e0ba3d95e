<% content_for :title, "Lesson Templates" %>
<%= render AdminPageBannerComponent.new(title: "Lesson Templates", links: [{ text: "+ New", path: new_admin_lesson_template_path }]) %>

<% if @templates_without_curricula_count > 0 %>
  <div class="mb-4">
    <%= render WarningBannerComponent.new(notes: "There are #{@templates_without_curricula_count} Lesson Templates with no curricula and so these are not valid for adding to a class. Examples: #{@templates_without_curricula_examples.join(', ')}#{'...' if @templates_without_curricula_count > 3}") %>
  </div>
<% end %>

<div class="mb-2">
  <%= render AdminPaginateComponent.new(items: @lesson_templates) %>
</div>
<div class="admin-table">
  <div class="admin-table-filters">
    <%= render AdminPageSearchComponent.new(index_path: admin_lesson_templates_path) %>
  </div>
  <table>
    <thead>
    <tr>
      <th></th>
      <th></th>
      <th>
        <%= sortable_link('machine_name', 'Machine Name'.strip, ->(params) { admin_lesson_templates_path(params) }) %>
      </th>
      <th>
        <%= sortable_link('name', 'Name'.strip, ->(params) { admin_lesson_templates_path(params) }) %>
      </th>
      <th class="hidden sm:table-cell">
        <%= sortable_link('created_at', 'Created'.strip, ->(params) { admin_lesson_templates_path(params) }) %>
      </th>
    </tr>
    </thead>
    <tbody>
    <% @lesson_templates.each do |template| %>
      <tr>
        <%= render TableActionsColumnComponent.new(links: [{ text: "Edit", path: edit_admin_lesson_template_path(template) }]) %>
        <%= render TableThumbnailColumnComponent.new(fileboy_id: template.fileboy_image_id) %>
        <td class="admin-table-primary-col"><%= template.machine_name %></td>
        <td class="admin-table-primary-col"><%= template.name %></td>
        <%= render TableDateColumnComponent.new(date: template.created_at, class_name: "hidden sm:table-cell") %>
      </tr>
    <% end %>
    </tbody>
  </table>
</div>
