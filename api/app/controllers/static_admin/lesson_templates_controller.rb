require 'json'

module StaticAdmin
  class LessonTemplatesController < StaticAdminController
    include Pa<PERSON>ationHelper
    before_action :set_lesson_template, only: %i[edit quiz update update_quiz relink destroy convert_from_quip delete presentation keywords lesson_plan update_lesson_plan documents proof check_proof_generation_status start_proof_generation careers add_career add_career_path remove_career_path]
    around_action :skip_bullet, if: -> { defined?(Bullet) }, only: %i[presentation]

    admin_section :curriculum

    # GET /admin/lesson-templates
    def index
      sortable_columns = %w[machine_name name created_at]
      sort = sortable_columns.include?(params[:sort]) ? params[:sort] : 'created_at'
      order = %w[asc desc].include?(params[:order]) ? params[:order] : 'desc'

      @lesson_templates = Lesson::Template.where(user_generated: false)

      @lesson_templates = @lesson_templates.where('machine_name ILIKE :query OR name ILIKE :query', query: "%#{params[:query]}%") if params[:query].present?

      @lesson_templates = @lesson_templates.select(:id, :fileboy_image_id, :machine_name, :name, :created_at).order(sort => order)
      @lesson_templates = safe_paginate(@lesson_templates, per_page: 50, page: params[:page])

      # Count lesson templates without curricula for warning banner
      @templates_without_curricula_count = Lesson::Template.left_joins(:new_library_curricula).where(user: nil, new_library_curricula: { id: nil }).distinct.count
      # Get a few examples for display
      @templates_without_curricula_examples = Lesson::Template.left_joins(:new_library_curricula)
                                                              .where(user: nil, new_library_curricula: { id: nil })
                                                              .distinct
                                                              .limit(3)
                                                              .pluck(:name)
    end

    def new
      @lesson_template = Lesson::Template.new()
    end

    def create
      @lesson_template = Lesson::Template.new(lesson_template_params)
      if @lesson_template.save
        redirect_to edit_admin_lesson_template_path(@lesson_template), notice: 'Template created'
      else
        render :new
      end
    rescue => e
      Rails.logger.error e
      render :new
    end

    # GET /admin/lesson-templates/:id/edit
    def edit; end

    # GET /admin/lesson-templates/:id/documents
    def documents; end

    # GET /admin/lesson-templates/:id/keywords
    def keywords
      @keywords = @lesson_template.keywords.includes([:quiz_question])
      @slides = @lesson_template.slides.ordered.map do |slide|
        name = slide.body.present? ? "#{slide.user_friendly_slide_type}: #{slide.body}" : slide.slide_type
        ["#{slide.weight} - #{name.truncate(40)}", slide.id]
      end
    end

    def generate_keywords_with_ai
      LessonKeywordsGenerator.new.generate_for_lesson(params[:id])
      redirect_to keywords_admin_lesson_template_path(params[:id]), notice: 'Keywords sucessfully generated'
    rescue => e
      Rails.logger.error("generate_with_ai error: #{e.class} - #{e.message}\n#{e.backtrace.take(5).join("\n")}")
      redirect_to keywords_admin_lesson_template_path(params[:id]), alert: 'Error generating keywords'
    end

    def generate_keyword_content_from_name
      keyword_name = params[:name]
      if keyword_name.blank?
        render json: { error: 'Name is required' }, status: :bad_request
        return
      end
      data = LessonKeywordsGenerator.new.generate_content_from_name(params[:id], keyword_name)
      render json: data
    end

    # GET /admin/lesson-templates/:id/delete
    def delete; end

    # PUT /admin/lesson-templates/:id
    def update
      if @lesson_template.update(lesson_template_params)
        redirect_to edit_admin_lesson_template_path(@lesson_template), notice: 'Template updated'
      else
        render :edit
      end
    rescue => e
      Rails.logger.error e
      render :edit
    end

    # PUT /admin/lesson-templates/:id/quiz
    def update_quiz
      quip_quiz_id = params.dig(:lesson_template, :quip_quiz_update_assoc_id)

      quip_quiz = nil
      if quip_quiz_id.present?
        quip_quiz = QuipQuiz.find_by(id: quip_quiz_id)
        unless quip_quiz
          flash.now[:alert] = "Quip Quiz with ID #{quip_quiz_id} was not found."
          return render :quiz
        end
      end

      if quip_quiz&.lesson_template.present?
        flash.now[:alert] = "Quip Quiz with ID #{quip_quiz_id} is already linked to a lesson template: '#{quip_quiz.lesson_template.name}'. Please unlink it first and then try again."
        return render :quiz
      end

      if @lesson_template.update(quip_quiz: quip_quiz)
        redirect_to quiz_admin_lesson_template_path(@lesson_template), notice: 'Template updated'
      else
        flash.now[:alert] = 'Failed to update lesson template.'
        render :quiz
      end
    end

    # GET /admin/lesson-templates/:id/proof
    def proof
      @current_user = current_user
    end

    def check_proof_generation_status
      cache_key = "lesson_template_proof_status_#{@lesson_template.id}"
      @current_proof_job = Rails.cache.read(cache_key)

      # clear the cache so it stops the page reloading constantly
      Rails.cache.delete(cache_key) if @current_proof_job && @current_proof_job[:status] == 'completed'

      if @current_proof_job.nil?
        render json: { status: 'not_started' }, status: :ok
      else
        render json: @current_proof_job, status: :ok
      end
    end

    def start_proof_generation
      # Check if a proof job is already running
      existing_job = Rails.cache.read("lesson_template_proof_status_#{@lesson_template.id}")

      # Allow override if cancel_current param is true
      if params[:cancel_current].present? && params[:cancel_current].to_s == 'true'
        Rails.cache.delete("lesson_template_proof_status_#{@lesson_template.id}")
        existing_job = nil
      end

      if existing_job && existing_job['status'] == 'processing'
        render json: { status: 'processing', message: 'Proof generation is already in progress for this template.' }, status: :ok
        return
      end

      render json: { status: 'processing', message: 'Proof generation started successfully.' }, status: :ok
      # Start a new proof generation job
      LessonTemplateProofJob.perform_later(@lesson_template)
    rescue => e
      Rails.logger.error("Error starting proof generation: #{e.class} - #{e.message}\n#{e.backtrace.take(5).join("\n")}")
      render json: { status: 'error', message: 'Failed to start proof generation.' }, status: :internal_server_error
    end

    # DELETE /admin/lesson-templates/:id
    def destroy
      if @lesson_template.lessons.exists?
        redirect_to edit_admin_lesson_template_path(@lesson_template), alert: 'Cannot delete template with associated lessons'
      else
        @lesson_template.destroy
        redirect_to admin_lesson_templates_path, notice: 'Template deleted'
      end
    end

    # GET /admin/lesson-templates/:id/quiz
    def quiz
      @quiz = @lesson_template.quip_quiz || QuipQuiz.new(lesson_template: @lesson_template)
    end

    # POST /admin/lesson-templates/:id/convert_from_quip
    def convert_from_quip
      # e9af0384-19cd-46b0-8553-c6b1f783e09e
      quip_quiz_key = @lesson_template.quip_quiz_key
      unless quip_quiz_key.match?(/\b[\w-]{36}\b/)
        redirect_to quiz_admin_lesson_template_path(@lesson_template), alert: 'Expected quiz key to be a UUID of a quip quiz key'
        return
      end

      result = QuipQuiz.duplicate_from_quip(quip_quiz_key, @lesson_template)
      if result && result[:saved]
        @lesson_template.update(quip_quiz_key: result[:record].id)
        redirect_to quiz_admin_lesson_template_path(@lesson_template), notice: 'Quiz converted'
        return
      end

      redirect_to quiz_admin_lesson_template_path(@lesson_template), alert: result&.dig(:error).presence || 'Unknown error while converting quiz'
    rescue ActiveRecord::RecordNotFound => e
      Rails.logger.error("convert_from_quip error: #{e.class} - #{e.message}\n#{e.backtrace.take(5).join("\n")}")
      redirect_to quiz_admin_lesson_template_path(@lesson_template), alert: 'Quiz not found'
    rescue => e
      Rails.logger.error("convert_from_quip error: #{e.class} - #{e.message}\n#{e.backtrace.take(5).join("\n")}")
      redirect_to quiz_admin_lesson_template_path(@lesson_template), alert: 'Unknown error while converting quiz'
    end

    # PUT /admin/lesson-templates/:id/relink
    def relink
      new_template_id = relink_params[:new_template_id]

      raise ActiveRecord::RecordNotFound if new_template_id.blank?

      new_template = Lesson::Template.find(new_template_id)

      raise 'Cannot relink lessons from a template to itself' if new_template.id == @lesson_template.id
      raise 'Cannot relink lessons from a user generated template to a system template or vice versa' if new_template.user_generated || @lesson_template.user_generated

      @lesson_template.lessons.update_all(template_id: new_template.id)
      redirect_to edit_admin_lesson_template_path(@lesson_template), notice: 'Lessons relinked'
    rescue ActiveRecord::RecordNotFound
      redirect_to edit_admin_lesson_template_path(@lesson_template), alert: 'Template not found'
    rescue ActiveRecord::RecordInvalid => e
      redirect_to edit_admin_lesson_template_path(@lesson_template), alert: "Relink failed: #{e.record.errors.full_messages.join(', ')}"
    rescue => e
      Rails.logger.error("Relink Error: #{e.message}")
      redirect_to edit_admin_lesson_template_path(@lesson_template), alert: 'Relink failed due to an unexpected error.'
    end

    # GET /admin/lesson-templates/:id/presentation
    def presentation
      @slides = @lesson_template.slides.ordered.includes(%i[video tour quip_question template]).where.not(slide_type: %w[intro outro])
    end

    def generate_with_ai
      PresentationGenerator.new.generate_for_lesson(params[:id])
      redirect_to presentation_admin_lesson_template_path(params[:id]), notice: 'Presentation generated'
    rescue => e
      Rails.logger.error("generate_with_ai error: #{e.class} - #{e.message}\n#{e.backtrace.take(5).join("\n")}")
      redirect_to presentation_admin_lesson_template_path(params[:id]), alert: 'Error generating presentation'
    end

    def lesson_plan; end

    def update_lesson_plan
      puts 'ASDASDASDAS'
      puts lesson_plan_params.inspect
      if @lesson_template.update!(lesson_plan_params)
        redirect_to lesson_plan_admin_lesson_template_path(@lesson_template), notice: 'Lesson plan updated'
      else
        render :lesson_plan
      end
    end

    def feedback
      @lesson_template = Lesson::Template.find(params[:id])
      @feedbacks = NewPresentationFeedback.includes(:user).where(associate_to: 'template', associate_to_id: params[:id])
    end

    def careers
      query = params[:query].presence || ''
      @careers = @lesson_template.career_paths.where('career_name ILIKE ?', "%#{query}%")
    end

    def add_career
      query = params[:query].presence || ''
      @careers = CareerPath.completed.where.not(id: @lesson_template.career_paths.ids).where('career_name ILIKE ?', "%#{query}%").order(:career_name)
      @careers = safe_paginate(@careers, per_page: 50, page: params[:page])
    end

    def add_career_path
      kwargs = params.permit(:query, :page)
      career_path = CareerPath.find_by(id: params[:career_path_id])
      redirect_to add_career_admin_lesson_template_path(@lesson_template, **kwargs), alert: 'Career path not found' and return if career_path.nil?
      @lesson_template.career_paths << career_path unless @lesson_template.career_paths.include?(career_path)
      redirect_to add_career_admin_lesson_template_path(@lesson_template, **kwargs), notice: 'Career path added'
    rescue => e
      Rails.logger.error("Error adding career path: #{e.class} - #{e.message}\n#{e.backtrace.take(5).join("\n")}")
      redirect_to add_career_admin_lesson_template_path(@lesson_template, **kwargs), alert: 'Error adding career path'
    end

    def remove_career_path
      kwargs = params.permit(:query, :page)
      career_path = CareerPath.find_by(id: params[:career_path_id])
      redirect_to careers_admin_lesson_template_path(@lesson_template, **kwargs), alert: 'Career path not found' and return if career_path.nil?
      @lesson_template.career_paths.delete(career_path)
      redirect_to careers_admin_lesson_template_path(@lesson_template, **kwargs), notice: 'Career path removed'
    rescue => e
      Rails.logger.error("Error removing career path: #{e.class} - #{e.message}\n#{e.backtrace.take(5).join("\n")}")
      redirect_to careers_admin_lesson_template_path(@lesson_template, **kwargs), alert: 'Error removing career path'
    end

    private

    def lesson_template_params
      params.require(:lesson_template).permit(
        :name,
        :machine_name,
        :anonymous,
        :demo,
        :disable_viewing,
        :available,
        :viewable_only,
        :fileboy_image_id,
        author_ids: [],
        objectives: [],
        scientific_enquiry_type_ids: []
      )
    end

    def lesson_plan_params
      params.require(:lesson_template).permit(
        :lesson_plan_layout,
        :intent,
        :new_lesson_plan_resources,
        :implementation,
        :impact_assessment,
        :specification,
        :ks4_learning_outcomes,
        :core_knowledge,
        :analogies_models,
        :ks4_teacher_mastery
      )
    end

    def relink_params
      params.require(:lesson_template).permit(:new_template_id)
    end

    def quiz_key_params
      params.require(:lesson_template).permit(:quip_quiz_key)
    end

    def set_lesson_template
      @lesson_template = Lesson::Template.find_by(id: params[:id])
      render 'static/not_found', status: :not_found and return if @lesson_template.nil?
    end
  end
end
